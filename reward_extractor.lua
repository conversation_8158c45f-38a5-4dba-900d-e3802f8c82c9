-- Reward Extractor Script
-- Uses environment functions to extract all rewards from GeanieQuestData and GemGeanie modules

local HttpService = game:GetService("HttpService")
local ReplicatedStorage = game:GetService("ReplicatedStorage")

-- Environment functions validation and setup
local function validateEnvironment()
    local missing = {}

    if not getloadedmodules then table.insert(missing, "getloadedmodules") end
    if not debug or not debug.getupvalues then table.insert(missing, "debug.getupvalues") end

    if #missing > 0 then
        warn("Missing environment functions: " .. table.concat(missing, ", "))
        return false
    end

    return true
end

-- Environment functions from environment
local getmodules = getmodules
local getscripts = getscripts
local getinstances = getinstances
local getloadedmodules = getloadedmodules
local toclipboard = toclipboard

local RewardExtractor = {}

-- Function to safely require a module
local function safeRequire(module)
    local success, result = pcall(function()
        return require(module)
    end)
    if success then
        return result
    else
        warn("Failed to require module: " .. tostring(module))
        return nil
    end
end

-- Function to find module by name with better matching
local function findModule(name)
    if not getloadedmodules then
        warn("getloadedmodules function not available")
        return nil
    end

    local modules = getloadedmodules()
    local exactMatch = nil
    local partialMatches = {}

    for _, module in pairs(modules) do
        if module.Name == name then
            exactMatch = module
            break
        elseif string.find(module.Name:lower(), name:lower()) then
            table.insert(partialMatches, module)
        end
    end

    -- Return exact match first, then first partial match
    return exactMatch or partialMatches[1]
end

-- Function to extract rewards from GeanieQuestData
function RewardExtractor:ExtractGeanieRewards()
    local rewards = {}
    print("Extracting Geanie rewards...")

    -- Try multiple paths to find the module
    local geanieModule = nil
    local attempts = {
        function() return ReplicatedStorage.Shared.Data.Quests.GenieQuest end,
        function() return ReplicatedStorage.Shared.Data.Quests.GeanieQuestData end,
        function() return findModule("GeanieQuestData") end,
        function() return findModule("GenieQuest") end,
        function() return findModule("Geanie") end
    }

    for i, attempt in ipairs(attempts) do
        local success, result = pcall(attempt)
        if success and result then
            geanieModule = result
            print("Found Geanie module using attempt " .. i)
            break
        end
    end

    if not geanieModule then
        warn("Could not find GeanieQuestData module")
        self:ExtractHardcodedRewards(rewards)
        return rewards
    end

    local geanieData = safeRequire(geanieModule)
    if not geanieData then
        warn("Failed to require GeanieQuestData module")
        self:ExtractHardcodedRewards(rewards)
        return rewards
    end

    -- Extract rewards from the module function and its upvalues
    if type(geanieData) == "function" and debug and debug.getupvalues then
        print("Analyzing function upvalues...")
        local success, upvalues = pcall(debug.getupvalues, geanieData)

        if success and upvalues then
            for i, upvalue in pairs(upvalues) do
                if type(upvalue) == "table" then
                    self:AnalyzeTableForRewards(upvalue, rewards, "GeanieQuest_Upvalue_" .. i)
                end
            end
        else
            warn("Failed to get upvalues from GeanieQuestData function")
        end
    end

    -- Also try to extract hardcoded rewards from the source
    self:ExtractHardcodedRewards(rewards)

    return rewards
end

-- Function to extract hardcoded rewards from the known structure
function RewardExtractor:ExtractHardcodedRewards(rewards)
    -- Based on the GeanieQuestData.lua analysis, add known rewards
    local knownRewards = {
        -- Powerups
        {Name = "Spin Ticket", Type = "Powerup", Amount = 1},
        {Name = "Golden Key", Type = "Powerup", Amount = 5},
        {Name = "Reroll Orb", Type = "Powerup", Amount = 20},
        {Name = "Power Orb", Type = "Powerup", Amount = 1},
        {Name = "Mystery Box", Type = "Powerup", Amount = 5},

        -- Potions Level 4
        {Name = "Lucky", Type = "Potion", Level = 4, Amount = 5},
        {Name = "Speed", Type = "Potion", Level = 4, Amount = 5},
        {Name = "Coins", Type = "Potion", Level = 4, Amount = 5},
        {Name = "Mythic", Type = "Potion", Level = 4, Amount = 5},

        -- Potions Level 5
        {Name = "Lucky", Type = "Potion", Level = 5, Amount = 1},
        {Name = "Speed", Type = "Potion", Level = 5, Amount = 1},
        {Name = "Coins", Type = "Potion", Level = 5, Amount = 1},
        {Name = "Mythic", Type = "Potion", Level = 5, Amount = 1},

        -- Potions Level 6
        {Name = "Lucky", Type = "Potion", Level = 6, Amount = 1},
        {Name = "Speed", Type = "Potion", Level = 6, Amount = 1},
        {Name = "Coins", Type = "Potion", Level = 6, Amount = 1},
        {Name = "Mythic", Type = "Potion", Level = 6, Amount = 1},

        -- Special Potions
        {Name = "Infinity Elixir", Type = "Potion", Amount = 1},

        -- Currency
        {Name = "Gems", Type = "Currency", Currency = "Gems"},
        {Name = "Coins", Type = "Currency", Currency = "Coins"},
    }

    for _, reward in pairs(knownRewards) do
        self:AddReward(reward, rewards, "GeanieQuestData_Hardcoded")
    end
end

-- Function to parse reward pool data
function RewardExtractor:ParseRewardPool(pool, rewards, poolName)
    if not pool then return end

    -- Handle LootPoolBuilder format
    if pool.Items then
        for _, item in pairs(pool.Items) do
            self:AddReward(item, rewards, poolName)
        end
    -- Handle direct array format
    elseif type(pool) == "table" and pool[1] then
        for _, item in pairs(pool) do
            if type(item) == "table" then
                self:AddReward(item, rewards, poolName)
            end
        end
    end
end

-- Function to add a reward to the collection
function RewardExtractor:AddReward(item, rewards, source)
    if not item or type(item) ~= "table" then return end

    local rewardData = {
        Name = item.Name or "Unknown",
        Type = item.Type or "Unknown",
        Level = item.Level or nil,
        Amount = item.Amount or 1,
        Source = source or "Unknown",
        Currency = item.Currency or nil
    }

    -- Create a unique key for the reward
    local key = rewardData.Name
    if rewardData.Level then
        key = key .. "_Level_" .. rewardData.Level
    end
    if rewardData.Type then
        key = key .. "_" .. rewardData.Type
    end

    if not rewards[key] then
        rewards[key] = rewardData
    else
        -- If reward already exists, add source info
        if not string.find(rewards[key].Source, source) then
            rewards[key].Source = rewards[key].Source .. ", " .. source
        end
    end
end

-- Function to extract rewards from GemGeanie module
function RewardExtractor:ExtractGemGeanieRewards()
    local rewards = {}

    -- Try to access GemGeanie module
    local success, gemGeanieModule = pcall(function()
        return ReplicatedStorage.Client.Gui.Frames.GemGeanie
    end)

    if not success then
        gemGeanieModule = findModule("GemGeanie")
    end

    if gemGeanieModule then
        local gemGeanieData = safeRequire(gemGeanieModule)
        if gemGeanieData then
            -- GemGeanie module contains UI logic, but may have reward references
            self:ParseModuleForRewards(gemGeanieData, rewards, "GemGeanie")
        end
    end

    return rewards
end

-- Function to extract rewards from all relevant modules
function RewardExtractor:ExtractAllRewards()
    local allRewards = {}

    -- Extract from GeanieQuestData
    local geanieRewards = self:ExtractGeanieRewards()
    for key, reward in pairs(geanieRewards) do
        allRewards[key] = reward
    end

    -- Extract from GemGeanie
    local gemGeanieRewards = self:ExtractGemGeanieRewards()
    for key, reward in pairs(gemGeanieRewards) do
        allRewards[key] = reward
    end

    -- Try to extract from other quest-related modules
    local modules = getloadedmodules()
    for _, module in pairs(modules) do
        local moduleName = module.Name:lower()
        if string.find(moduleName, "quest") or
           string.find(moduleName, "reward") or
           string.find(moduleName, "loot") or
           string.find(moduleName, "genie") or
           string.find(moduleName, "gem") then

            local moduleData = safeRequire(module)
            if moduleData and type(moduleData) == "table" then
                self:ParseModuleForRewards(moduleData, allRewards, module.Name)
            end
        end
    end

    -- Also try to extract from Constants module for additional reward data
    self:ExtractConstantsRewards(allRewards)

    return allRewards
end

-- Function to extract rewards from Constants module
function RewardExtractor:ExtractConstantsRewards(rewards)
    local success, constantsModule = pcall(function()
        return ReplicatedStorage.Shared.Constants
    end)

    if success then
        local constantsData = safeRequire(constantsModule)
        if constantsData and type(constantsData) == "table" then
            -- Look for reward-related constants
            for key, value in pairs(constantsData) do
                if type(value) == "table" and (
                    string.find(key:lower(), "reward") or
                    string.find(key:lower(), "loot") or
                    string.find(key:lower(), "genie") or
                    string.find(key:lower(), "tier")
                ) then
                    self:ParseModuleForRewards(value, rewards, "Constants_" .. key)
                end
            end
        end
    end
end

-- Function to parse any module for reward data
function RewardExtractor:ParseModuleForRewards(moduleData, rewards, moduleName)
    if type(moduleData) ~= "table" then return end

    for key, value in pairs(moduleData) do
        if type(value) == "table" then
            -- Look for reward-like structures
            if value.Type and value.Name then
                self:AddReward(value, rewards, moduleName)
            elseif value.Items then
                self:ParseRewardPool(value, rewards, moduleName .. "_" .. key)
            elseif type(value[1]) == "table" and value[1].Type then
                for _, item in pairs(value) do
                    self:AddReward(item, rewards, moduleName .. "_" .. key)
                end
            end
        end
    end
end

-- Function to format rewards as a table
function RewardExtractor:FormatRewardsTable(rewards)
    local tableData = {
        "| Name | Type | Level | Amount | Source |",
        "|------|------|-------|--------|--------|"
    }

    -- Sort rewards by name
    local sortedKeys = {}
    for key in pairs(rewards) do
        table.insert(sortedKeys, key)
    end
    table.sort(sortedKeys)

    for _, key in pairs(sortedKeys) do
        local reward = rewards[key]
        local level = reward.Level and tostring(reward.Level) or "N/A"
        local amount = reward.Amount and tostring(reward.Amount) or "1"

        local row = string.format("| %s | %s | %s | %s | %s |",
            reward.Name,
            reward.Type,
            level,
            amount,
            reward.Source
        )
        table.insert(tableData, row)
    end

    return table.concat(tableData, "\n")
end

-- Main execution function
function RewardExtractor:Execute()
    print("=== Reward Extractor Starting ===")

    -- Extract all rewards
    local rewards = self:ExtractAllRewards()

    -- Format as table
    local rewardTable = self:FormatRewardsTable(rewards)

    -- Output results
    print("\n=== REWARDS TABLE ===")
    print(rewardTable)

    -- Copy to clipboard if available
    if toclipboard then
        toclipboard(rewardTable)
        print("\n=== Table copied to clipboard ===")
    end

    -- Also output as JSON for further processing
    local jsonData = HttpService:JSONEncode(rewards)
    print("\n=== JSON DATA ===")
    print(jsonData)

    return rewards, rewardTable
end

-- Execute the extractor
return RewardExtractor:Execute()
